// create-icon.js - <PERSON>ript to create Windows icon from PNG
// This script helps convert PNG to ICO format for Windows installer

const fs = require('fs');
const path = require('path');

console.log('ACTSE Hardware Bridge - Icon Creation Helper');
console.log('===========================================');

// Check if logo.png exists
const logoPath = path.join(__dirname, 'src', 'logo.png');
const icoPath = path.join(__dirname, 'src', 'logo.ico');

if (fs.existsSync(logoPath)) {
    console.log('✓ Found logo.png in src/ directory');
    
    if (fs.existsSync(icoPath)) {
        console.log('✓ logo.ico already exists');
    } else {
        console.log('⚠ logo.ico not found');
        console.log('\nTo create a Windows icon file, you have several options:');
        console.log('\n1. Online Converter (Recommended):');
        console.log('   - Go to https://convertio.co/png-ico/');
        console.log('   - Upload your src/logo.png file');
        console.log('   - Download the converted logo.ico');
        console.log('   - Save it as src/logo.ico');
        
        console.log('\n2. Using ImageMagick (if installed):');
        console.log('   magick src/logo.png -define icon:auto-resize=256,128,64,48,32,16 src/logo.ico');
        
        console.log('\n3. Using GIMP:');
        console.log('   - Open src/logo.png in GIMP');
        console.log('   - File > Export As > logo.ico');
        console.log('   - Choose multiple sizes: 16x16, 32x32, 48x48, 64x64, 128x128, 256x256');
        
        console.log('\n4. Using online tool:');
        console.log('   - https://icoconvert.com/');
        console.log('   - Upload PNG and download ICO');
        
        console.log('\nAfter creating logo.ico, run: npm run build:win');
    }
} else {
    console.log('✗ logo.png not found in src/ directory');
    console.log('\nPlease add your logo file as src/logo.png first');
}

console.log('\n===========================================');
console.log('Build Commands:');
console.log('npm run build:win    - Build for Windows (32-bit and 64-bit)');
console.log('npm run build:win32  - Build for Windows 32-bit only');
console.log('npm run build:win64  - Build for Windows 64-bit only');
console.log('npm run dist         - Build distribution package');
console.log('===========================================');
