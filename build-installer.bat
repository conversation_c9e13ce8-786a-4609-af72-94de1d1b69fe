@echo off
echo ========================================
echo ACTSE Hardware Bridge - Build Installer
echo ========================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

REM Check if npm is available
npm --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: npm is not available
    pause
    exit /b 1
)

echo Node.js and npm are available
echo.

REM Check if logo.ico exists
if not exist "src\logo.ico" (
    echo WARNING: src\logo.ico not found
    echo.
    echo Please create a Windows icon file with 256x256 size:
    echo 1. Convert src\logo.png to ICO format with 256x256 resolution
    echo 2. Save as src\logo.ico
    echo 3. Run this script again
    echo.
    echo The icon must include these sizes: 16x16, 32x32, 48x48, 64x64, 128x128, 256x256
    echo You can use online converters like:
    echo - https://convertio.co/png-ico/
    echo - https://icoconvert.com/
    echo.
    pause
    exit /b 1
)

echo ✓ Logo icon found
echo.

REM Install dependencies if needed
if not exist "node_modules" (
    echo Installing dependencies...
    npm install
    if errorlevel 1 (
        echo ERROR: Failed to install dependencies
        pause
        exit /b 1
    )
    echo.
)

REM Check if electron-builder is installed
npm list electron-builder >nul 2>&1
if errorlevel 1 (
    echo Installing electron-builder...
    npm install --save-dev electron-builder
    if errorlevel 1 (
        echo ERROR: Failed to install electron-builder
        pause
        exit /b 1
    )
    echo.
)

echo ✓ Dependencies ready
echo.

REM Clean previous builds
if exist "dist" (
    echo Cleaning previous builds...
    rmdir /s /q dist
    echo.
)

REM Build the installer
echo Building Windows installer...
echo This may take a few minutes...
echo.

npm run build:win
if errorlevel 1 (
    echo.
    echo ERROR: Build failed
    echo Check the error messages above
    pause
    exit /b 1
)

echo.
echo ========================================
echo BUILD COMPLETED SUCCESSFULLY!
echo ========================================
echo.

REM Show build results
if exist "dist" (
    echo Installer created in dist\ directory:
    dir /b dist\*.exe 2>nul
    echo.
    
    echo Files created:
    for %%f in (dist\*.exe) do (
        echo ✓ %%f
        echo   Size: 
        for %%s in (%%f) do echo   %%~zs bytes
    )
    echo.
    
    echo The installer is ready for distribution!
    echo.
    echo To test the installer:
    echo 1. Run the .exe file as administrator
    echo 2. Follow the installation wizard
    echo 3. Test the auto-startup feature
    echo.
    
    REM Ask if user wants to open the dist folder
    set /p choice="Open dist folder? (y/n): "
    if /i "%choice%"=="y" (
        explorer dist
    )
) else (
    echo ERROR: No installer files found in dist directory
)

echo.
echo Press any key to exit...
pause >nul
