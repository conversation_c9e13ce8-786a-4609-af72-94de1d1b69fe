// main.js

const { app, BrowserWindow, ipc<PERSON>ain, <PERSON>u } = require('electron');
const path = require('path');
const Store = require('electron-store');
const fs = require('fs');

// --- ส่วนของ Bridge Logic ---
const { io } = require('socket.io-client');
const { Devices, CommandApdu } = require('smartcard');
const iconv = require('iconv-lite');

// --- ตัวแปร Global ---
let mainWindow;
let socket = null;
let devices = null;
let addressDatabase = [];
let connectedReaders = new Map(); // Track connected readers

// --- การจัดการ Config ด้วย electron-store ---
const store = new Store({
    defaults: {
        environment: "local",
        clinicId: "caremat",
        stationId: "Registration_Point_1",
        vpsUrl: "ws://localhost:3000",
        apiKey: "caremat_secret_key_for_reg_point_1_abcdef123456"
    }
});

// --- Environment Presets ---
const environmentPresets = {
    local: {
        vpsUrl: "ws://localhost:3000",
        apiKey: "caremat_secret_key_for_reg_point_1_abcdef123456"
    },
    production: {
        vpsUrl: "wss://ws.hompok.com",
        apiKey: "caremat_secret_key_for_reg_point_1_abcdef123456"
    }
};

// --- ฟังก์ชันสำหรับส่งข้อมูลไปที่หน้า UI ---
function sendToUI(channel, data) {
    if (mainWindow) {
        mainWindow.webContents.send(channel, data);
    }
}

// --- ฟังก์ชันสำหรับส่งข้อมูลไปยัง WebSocket Server ---
function sendMessageToVps(status, payload) {
    const config = store.get();
    const BRIDGE_ID = `${config.clinicId}::${config.stationId}`;
    if (socket && socket.connected) {
        const message = { source: BRIDGE_ID, status, payload };
        socket.emit('bridgeEvent', message);
        sendToUI('log', `[WSS ->] ส่ง Event '${status}' ไปยัง Server`);
    } else {
        sendToUI('log', `[WARN] ไม่สามารถส่ง Event '${status}' ได้เนื่องจากไม่ได้เชื่อมต่อกับ Server`);
    }
}

// --- โหลดฐานข้อมูลที่อยู่ ---
function loadAddressDatabase() {
    try {
        const dbPath = app.isPackaged 
            ? path.join(process.resourcesPath, 'th-address-db.json')
            : path.join(__dirname, 'th-address-db.json');
        addressDatabase = JSON.parse(fs.readFileSync(dbPath, 'utf8'));
        sendToUI('log', '[INFO] โหลดฐานข้อมูลที่อยู่สำเร็จ!');
    } catch (error) {
        sendToUI('log', `[WARN] ไม่พบไฟล์ th-address-db.json: ${error.message}`);
    }
}

// --- โค้ดเชื่อมต่อ WebSocket ---
function connectToVps() {
    const config = store.get();
    const BRIDGE_ID = `${config.clinicId}::${config.stationId}`;
    
    if (socket && socket.connected) {
        socket.disconnect();
    }

    sendToUI('log', `====================================================`);
    sendToUI('log', `  Hardware Bridge ID: [${BRIDGE_ID}]`);
    sendToUI('log', `  กำลังเชื่อมต่อกับ Server: ${config.vpsUrl}`);
    sendToUI('log', `====================================================`);
    sendToUI('status-update', { type: 'connection', status: 'connecting' });
    
    socket = io(config.vpsUrl, {
        reconnectionDelayMax: 10000,
        auth: { apiKey: config.apiKey, bridgeId: BRIDGE_ID }
    });

    socket.on('connect', () => {
        sendToUI('log', `[INFO] เชื่อมต่อกับ VPS สำเร็จ! (Socket ID: ${socket.id})`);
        sendToUI('status-update', { type: 'connection', status: 'connected' });
    });

    socket.on('disconnect', (reason) => {
        sendToUI('log', `[WARN] การเชื่อมต่อกับ VPS ถูกตัดขาด! Reason: ${reason}`);
        sendToUI('status-update', { type: 'connection', status: 'disconnected' });
    });

    socket.on('connect_error', (err) => {
        sendToUI('log', `[ERROR] เกิดข้อผิดพลาดในการเชื่อมต่อ: ${err.message}`);
        sendToUI('status-update', { type: 'connection', status: 'error', message: err.message });
    });

    // Handle status requests from monitor clients
    socket.on('statusRequest', (request) => {
        sendToUI('log', `[INFO] ได้รับคำขอสถานะจาก Monitor Client`);

        const { requesterId, requesterType } = request;

        // Get current reader status
        const readerStatus = getCurrentReaderStatus();
        const cardStatus = getCurrentCardStatus();

        // Send status response back to server
        const response = {
            requesterId: requesterId,
            requesterType: requesterType,
            readerStatus: readerStatus,
            cardStatus: cardStatus
        };

        socket.emit('statusResponse', response);
        sendToUI('log', `[INFO] ส่งสถานะปัจจุบันให้ Monitor Client แล้ว`);
    });
}

// --- โค้ดอ่านบัตรประชาชน ---
async function readThaiIDData(card) {
    try {
        // Validate card object
        if (!card) {
            throw new Error('Card object is null or undefined');
        }

        sendToUI('log', '[DEBUG] เริ่มกระบวนการอ่านข้อมูลบัตร...');

        const issueCommand = (commandBytes) => new Promise((resolve, reject) => {
            try {
                card.issueCommand(new CommandApdu({ bytes: commandBytes }), (err, res) => {
                    if (err) return reject(new Error(`Card command error: ${err}`));
                    if (!res) return reject(new Error('การตอบกลับจากบัตรไม่ถูกต้อง'));
                    if (!Buffer.isBuffer(res)) return reject(new Error('Invalid response format from card'));
                    resolve(res);
                });
            } catch (error) {
                reject(new Error(`Command execution error: ${error.message}`));
            }
        });

        const getData = async (requestApdu) => {
            try {
                let response = await issueCommand(requestApdu);

                // Validate response buffer
                if (!Buffer.isBuffer(response) || response.length < 2) {
                    throw new Error('Invalid response buffer from card');
                }

                let sw1 = response.slice(-2, -1).toString('hex');
                if (sw1 === '61') {
                    const getResponseLength = parseInt(response.slice(-1).toString('hex'), 16);
                    if (isNaN(getResponseLength) || getResponseLength <= 0) {
                        throw new Error('Invalid response length from card');
                    }
                    response = await issueCommand([0x00, 0xC0, 0x00, 0x00, getResponseLength]);
                }

                if (response.slice(-2).toString('hex') === '9000') {
                    return response.slice(0, -2);
                }
                throw new Error(`คำสั่งล้มเหลว (SW=${response.slice(-2).toString('hex')})`);
            } catch (error) {
                throw new Error(`getData error: ${error.message}`);
            }
        };

        await getData([0x00, 0xA4, 0x04, 0x00, 0x08, 0xA0, 0x00, 0x00, 0x00, 0x54, 0x48, 0x00, 0x01]);
        sendToUI('log', '[DEBUG] เลือกแอปเพล็ตบัตรประชาชนสำเร็จ');

        const formatAsDate = (rawDate) => `${rawDate.substring(6, 8)}/${rawDate.substring(4, 6)}/${rawDate.substring(0, 4)}`;
        const cleanAndTrim = (buffer) => iconv.decode(buffer, 'tis-620').replace(/\x00/g, '').trim();

        const citizenId = cleanAndTrim(await getData([0x80, 0xb0, 0x00, 0x04, 0x02, 0x00, 0x0d]));
        const fullNameTHRaw = cleanAndTrim(await getData([0x80, 0xb0, 0x00, 0x11, 0x02, 0x00, 0x64]));
        const fullNameENRaw = cleanAndTrim(await getData([0x80, 0xb0, 0x00, 0x75, 0x02, 0x00, 0x64]));
        const dobRaw = cleanAndTrim(await getData([0x80, 0xb0, 0x00, 0xD9, 0x02, 0x00, 0x08]));
        const genderRaw = cleanAndTrim(await getData([0x80, 0xb0, 0x00, 0xE1, 0x02, 0x00, 0x01]));
        const issuerRaw = cleanAndTrim(await getData([0x80, 0xb0, 0x00, 0xF6, 0x02, 0x00, 0x64]));
        const issueDateRaw = cleanAndTrim(await getData([0x80, 0xb0, 0x01, 0x67, 0x02, 0x00, 0x08]));
        const expireDateRaw = cleanAndTrim(await getData([0x80, 0xB0, 0x01, 0x6F, 0x02, 0x00, 0x08]));
        const addressRaw = cleanAndTrim(await getData([0x80, 0xb0, 0x15, 0x79, 0x02, 0x00, 0xA0]));
        
        let religion = 'ไม่ระบุ';
        try {
            const religionRaw = cleanAndTrim(await getData([0x80, 0xb0, 0x01, 0x77, 0x02, 0x00, 0x02]));
            religion = religionRaw.trim();
        } catch (e) {
            sendToUI('log', '[DEBUG] ไม่พบข้อมูลศาสนาบนบัตร หรืออ่านไม่สำเร็จ');
        }

        let photoBuffer = Buffer.alloc(0);
        const photoCommands = [
            [0x80,0xb0,0x01,0x7B,0x02,0x00,0xff], [0x80,0xb0,0x02,0x7A,0x02,0x00,0xff],
            [0x80,0xb0,0x03,0x79,0x02,0x00,0xff], [0x80,0xb0,0x04,0x78,0x02,0x00,0xff],
            [0x80,0xb0,0x05,0x77,0x02,0x00,0xff], [0x80,0xb0,0x06,0x76,0x02,0x00,0xff],
            [0x80,0xb0,0x07,0x75,0x02,0x00,0xff], [0x80,0xb0,0x08,0x74,0x02,0x00,0xff],
            [0x80,0xb0,0x09,0x73,0x02,0x00,0xff], [0x80,0xb0,0x0A,0x72,0x02,0x00,0xff],
            [0x80,0xb0,0x0B,0x71,0x02,0x00,0xff], [0x80,0xb0,0x0C,0x70,0x02,0x00,0xff],
            [0x80,0xb0,0x0D,0x6F,0x02,0x00,0xff], [0x80,0xb0,0x0E,0x6E,0x02,0x00,0xff],
            [0x80,0xb0,0x0F,0x6D,0x02,0x00,0xff], [0x80,0xb0,0x10,0x6C,0x02,0x00,0xff],
            [0x80,0xb0,0x11,0x6B,0x02,0x00,0xff], [0x80,0xb0,0x12,0x6A,0x02,0x00,0xff],
            [0x80,0xb0,0x13,0x69,0x02,0x00,0xff], [0x80,0xb0,0x14,0x68,0x02,0x00,0xff],
        ];

        for (const cmd of photoCommands) {
            try {
                const segment = await getData(cmd);
                photoBuffer = Buffer.concat([photoBuffer, segment]);
            } catch (e) {
                sendToUI('log', `[DEBUG] อ่านรูปภาพจบ (อาจไม่ครบ 20 ส่วน)`);
                break;
            }
        }
        const photoBase64 = photoBuffer.length > 0 ? `data:image/jpeg;base64,${photoBuffer.toString('base64')}` : null;

        const [titleTH, firstNameTH, ...lastNameTHParts] = fullNameTHRaw.split('#');
        const [titleEN, firstNameEN, ...lastNameENParts] = fullNameENRaw.split('#');
        const addressParts = addressRaw.split('#');
        
        const calculateAge = (startDateString, endDateString = null) => {
            const startYearBE = parseInt(startDateString.substring(0, 4), 10);
            const startMonth = parseInt(startDateString.substring(4, 6), 10) - 1;
            const startDay = parseInt(startDateString.substring(6, 8), 10);
            const startDate = new Date(startYearBE - 543, startMonth, startDay);
            let endDate;
            if (endDateString) {
                const endYearBE = parseInt(endDateString.substring(0, 4), 10);
                const endMonth = parseInt(endDateString.substring(4, 6), 10) - 1;
                const endDay = parseInt(endDateString.substring(6, 8), 10);
                endDate = new Date(endYearBE - 543, endMonth, endDay);
            } else {
                endDate = new Date();
            }
            if (startDate > endDate) return "0 ปี 0 เดือน 0 วัน";
            let years = endDate.getFullYear() - startDate.getFullYear();
            let months = endDate.getMonth() - startDate.getMonth();
            let days = endDate.getDate() - startDate.getDate();
            if (days < 0) {
                months--;
                const lastDayOfPrevMonth = new Date(endDate.getFullYear(), endDate.getMonth(), 0).getDate();
                days += lastDayOfPrevMonth;
            }
            if (months < 0) {
                years--;
                months += 12;
            }
            return `${years} ปี ${months} เดือน ${days} วัน`;
        };
        
        const province = (addressParts[7] || '').replace(/^(จังหวัด|จ\.)/, '').trim();
        const district = (addressParts[6] || '').replace(/^(อำเภอ|อ\.)/, '').trim();
        const subDistrict = (addressParts[5] || '').replace(/^(ตำบล|ต\.)/, '').trim();
        
        let postcode = '';
        if (addressDatabase.length > 0) {
            const provinceData = addressDatabase.find(p => p.name_th === province);
            if (provinceData) {
                const districtData = provinceData.amphure.find(d => d.name_th === district);
                if (districtData) {
                    const subDistrictData = districtData.tambon.find(s => s.name_th === subDistrict);
                    if (subDistrictData) {
                        postcode = subDistrictData.zip_code.toString();
                    }
                }
            }
        }
        sendToUI('log', `[DEBUG] ค้นหารหัสไปรษณีย์สำหรับ ${subDistrict}, ${district}, ${province}: ${postcode || 'ไม่พบ'}`);

        const cardData = {
            citizenId,
            titleTH: titleTH.trim(),
            firstNameTH: firstNameTH.trim(),
            lastNameTH: lastNameTHParts.join(' ').trim(),
            titleEN: titleEN.trim(),
            firstNameEN: firstNameEN.trim(),
            lastNameEN: lastNameENParts.join(' ').trim(),
            dob: formatAsDate(dobRaw),
            gender: genderRaw === '1' ? 'ชาย' : (genderRaw === '2' ? 'หญิง' : 'ไม่ระบุ'),
            issuer: issuerRaw.trim(),
            issueDate: formatAsDate(issueDateRaw),
            expireDate: formatAsDate(expireDateRaw),
            religion: religion,
            addressFull: addressRaw.replace(/#+/g, ' ').trim(),
            address: {
                houseNo: (addressParts[0] || '').trim(),
                villageNo: (addressParts[1] || '').trim(),
                lane: (addressParts[2] || '').trim(),
                road: (addressParts[3] || '').trim(),
                building: (addressParts[4] || '').trim(),
                subDistrict,
                district,
                province,
                postcode,
            },
            currentAge: calculateAge(dobRaw),
            ageAtIssueDate: calculateAge(dobRaw, issueDateRaw),
            cardAge: calculateAge(issueDateRaw),
            photoBase64,
        };
        
        sendToUI('log', `[SUCCESS] อ่านข้อมูลสำเร็จ: ${fullNameTHRaw}`);
        sendToUI('status-update', { type: 'card', status: 'read_success', data: cardData });
        setCurrentCardStatus('read_success', cardData);
        sendMessageToVps('card_read_success', { data: cardData });

    } catch (error) {
        sendToUI('log', `[ERROR] อ่านบัตรล้มเหลว: ${error.message}`);
        sendToUI('status-update', { type: 'card', status: 'read_error', message: error.message });
        sendMessageToVps('card_read_error', { message: error.message });
    }
}

// --- ฟังก์ชันตรวจสอบสถานะเครื่องอ่านบัตร ---
function updateReaderStatus() {
    if (connectedReaders.size > 0) {
        // If there are connected readers, show the first one
        const firstReader = connectedReaders.keys().next().value;
        sendToUI('status-update', { type: 'reader', status: 'connected', name: firstReader });
    } else {
        // No readers connected
        sendToUI('status-update', { type: 'reader', status: 'disconnected' });
    }
}

// --- ฟังก์ชันสำหรับดึงสถานะปัจจุบัน ---
function getCurrentReaderStatus() {
    if (connectedReaders.size > 0) {
        const firstReader = connectedReaders.keys().next().value;
        return {
            status: 'connected',
            name: firstReader
        };
    } else {
        return {
            status: 'disconnected',
            name: null
        };
    }
}

let currentCardStatus = { status: 'removed' }; // Track current card status

function getCurrentCardStatus() {
    return currentCardStatus;
}

function setCurrentCardStatus(status, data = null, message = null) {
    currentCardStatus = {
        status: status,
        data: data,
        message: message,
        timestamp: new Date().toISOString()
    };
}

// --- การจัดการ Smart Card ---
function startSmartcardService() {
    // Prevent multiple instances of smart card service
    if (devices) {
        sendToUI('log', '[WARN] Smart Card service is already running');
        return;
    }

    try {
        sendToUI('log', '[INFO] กำลังเริ่มบริการ Smart Card...');
        devices = new Devices();

        // Initial status check
        setTimeout(() => {
            updateReaderStatus();
        }, 1000);
    } catch (error) {
        sendToUI('log', `[ERROR] ไม่สามารถเริ่มบริการ Smart Card ได้: ${error.message}`);
        devices = null;
        throw error;
    }

    devices.on('device-activated', event => {
        const payload = { readerName: event.device.name };
        connectedReaders.set(event.device.name, event.device);
        sendToUI('log', `[INFO] พบเครื่องอ่านบัตร: ${payload.readerName}`);
        sendToUI('status-update', { type: 'reader', status: 'connected', name: payload.readerName });
        sendMessageToVps('reader_connected', payload);

        event.device.on('card-inserted', async (cardEvent) => {
            try {
                sendToUI('log', `[INFO] พบบัตรในเครื่องอ่าน ${event.device.name}`);
                // Confirm reader is still connected when card is inserted
                sendToUI('status-update', { type: 'reader', status: 'connected', name: event.device.name });
                sendToUI('status-update', { type: 'card', status: 'reading' });
                setCurrentCardStatus('reading');
                sendMessageToVps('reading', { message: 'กำลังอ่านข้อมูล...' });

                // Add timeout for card reading
                const timeoutPromise = new Promise((_, reject) => {
                    setTimeout(() => reject(new Error('Card reading timeout (30 seconds)')), 30000);
                });

                await Promise.race([
                    readThaiIDData(cardEvent.card),
                    timeoutPromise
                ]);
            } catch (error) {
                sendToUI('log', `[ERROR] Card insertion handling error: ${error.message}`);
                sendToUI('status-update', { type: 'card', status: 'read_error', message: error.message });
                setCurrentCardStatus('read_error', null, error.message);
                sendMessageToVps('card_read_error', { message: error.message });
            }
        });

        event.device.on('card-removed', () => {
            sendToUI('log', `[INFO] นำบัตรออกจากเครื่องอ่าน ${event.device.name}`);
            // Confirm reader is still connected when card is removed
            sendToUI('status-update', { type: 'reader', status: 'connected', name: event.device.name });
            sendToUI('status-update', { type: 'card', status: 'removed' });
            setCurrentCardStatus('removed');
            sendMessageToVps('card_removed', {});
        });

        event.device.on('error', (err) => {
            const errorMessage = err?.message || err?.toString() || 'Unknown reader error';
            sendToUI('log', `[ERROR] Reader Error (${event.device.name}): ${errorMessage}`);
            sendToUI('status-update', { type: 'reader', status: 'error', message: errorMessage });
            // Reset card status when reader has error
            sendToUI('status-update', { type: 'card', status: 'removed' });
        });
    });

    devices.on('device-deactivated', event => {
        const payload = { readerName: event.device.name };
        connectedReaders.delete(event.device.name);
        sendToUI('log', `[WARN] เครื่องอ่าน ${payload.readerName} ถูกถอดออก`);
        sendToUI('status-update', { type: 'reader', status: 'disconnected', name: payload.readerName });
        sendMessageToVps('reader_disconnected', payload);
    });

    devices.on('error', (error) => {
        const errorMessage = error?.message || error?.toString() || 'Unknown smart card service error';
        sendToUI('log', `[FATAL] Smart Card service error: ${errorMessage}`);
        sendToUI('status-update', { type: 'reader', status: 'fatal_error', message: errorMessage });
        sendToUI('status-update', { type: 'card', status: 'removed' });

        // Try to restart the service after a delay
        setTimeout(() => {
            sendToUI('log', '[INFO] พยายามเริ่มบริการ Smart Card ใหม่...');
            try {
                cleanupSmartcardService(); // Clean up first
                startSmartcardService();
            } catch (restartError) {
                sendToUI('log', `[ERROR] ไม่สามารถเริ่มบริการใหม่ได้: ${restartError.message}`);
            }
        }, 5000);
    });
}

// --- Electron App Lifecycle ---
function createWindow() {
    mainWindow = new BrowserWindow({
        width: 900,
        height: 680,
        icon: path.join(__dirname, 'src/logo.png'),
        webPreferences: {
            preload: path.join(__dirname, 'preload.js'),
            contextIsolation: true,
            nodeIntegration: false
        }
    });

    mainWindow.webContents.on('did-finish-load', () => {
        sendToUI('log', 'UI พร้อมทำงาน, กำลังเริ่ม Bridge...');
        sendToUI('log', '[INFO] Single Instance Lock: ป้องกันการเปิดแอปพลิเคชันซ้ำ');
        loadAddressDatabase();
        startSmartcardService();
        connectToVps();
    });

    mainWindow.loadFile('src/index.html');
}

// --- Global Error Handling ---
process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception:', error);
    if (mainWindow) {
        sendToUI('log', `[ERROR] Uncaught Exception: ${error.message}`);
        sendToUI('log', `[DEBUG] Stack: ${error.stack}`);
    }
    // Don't exit the process, just log the error
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
    if (mainWindow) {
        sendToUI('log', `[ERROR] Unhandled Promise Rejection: ${reason}`);
    }
});

// --- Single Instance Lock ---
const gotTheLock = app.requestSingleInstanceLock();

if (!gotTheLock) {
    // Another instance is already running
    console.log('Another instance of ACTSE Hardware Bridge is already running. Exiting...');
    app.quit();
} else {
    // This is the first instance
    app.on('second-instance', (event, commandLine, workingDirectory) => {
        // Someone tried to run a second instance, focus our window instead
        if (mainWindow) {
            if (mainWindow.isMinimized()) mainWindow.restore();
            mainWindow.focus();
            sendToUI('log', '[WARN] พยายามเปิดแอปพลิเคชันซ้ำ - กำลังโฟกัสหน้าต่างที่มีอยู่');
        }
    });

    app.whenReady().then(() => {
        createWindow();

        app.on('activate', () => {
            if (BrowserWindow.getAllWindows().length === 0) createWindow();
        });
    });
}

app.on('window-all-closed', () => {
    // Clean up smart card service before quitting
    cleanupSmartcardService();
    if (process.platform !== 'darwin') app.quit();
});

app.on('before-quit', () => {
    // Clean up smart card service before quitting
    cleanupSmartcardService();
});

// --- Cleanup Function ---
function cleanupSmartcardService() {
    if (devices) {
        try {
            sendToUI('log', '[INFO] กำลังปิดบริการ Smart Card...');
            devices.removeAllListeners();
            devices = null;
            connectedReaders.clear();
        } catch (error) {
            console.error('Error cleaning up smart card service:', error);
        }
    }
}

// --- IPC Handlers ---
ipcMain.handle('get-config', () => store.get());
ipcMain.handle('get-environment-presets', () => environmentPresets);
ipcMain.handle('save-config', (event, config) => {
    store.set(config);
    sendToUI('log', '[INFO] บันทึกการตั้งค่าใหม่แล้ว, กำลังเชื่อมต่อใหม่...');
    connectToVps();
});
