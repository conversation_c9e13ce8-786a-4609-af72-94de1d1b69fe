// local-print-test.js
// ทดสอบการพิมพ์ Thermal Printer ด้วยภาษาอังกฤษล้วน

const { ThermalPrinter, PrinterTypes } = require('node-thermal-printer');
const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');

// *** แก้ไขให้ตรงกับชื่อเครื่องพิมพ์ของคุณ ***
const PRINTER_NAME = "Xprinter-XP58";

const DUMMY_RECEIPT_DATA = {
  items: [
    { name: "General Checkup", quantity: 1, price: 1500.00 },
    { name: "Nurse Service", quantity: 1, price: 100.00 },
    { name: "Paracetamol", quantity: 1, price: 20.00 },
  ],
  total: 1620.00,
  paymentMethod: "Cash"
};

async function testPrint() {
  console.log(`[INFO] Starting print test to printer: ${PRINTER_NAME}`);

  const printer = new ThermalPrinter({
    type: PrinterTypes.EPSON,
    // interface: '/dev/null', // mock interface (not actually used for output)
    interface: 'usb',
    characterSet: 'PC437_USA',
    width: 42
  });

  printer.raw(Buffer.from([0x1B, 0x40])); // Reset printer

  printer.alignCenter();
  printer.bold(true);
  printer.println("--- TEST RECEIPT ---");
  printer.bold(false);
  printer.println("Homepok Medical Clinic");
  printer.drawLine();

  printer.alignLeft();
  printer.println(`Date: ${new Date().toLocaleString()}`);
  printer.drawLine();

  printer.tableCustom([
    { text: "Item", align: "LEFT", width: 0.6 },
    { text: "Qty", align: "CENTER", width: 0.15 },
    { text: "Price", align: "RIGHT", width: 0.25 }
  ]);

  DUMMY_RECEIPT_DATA.items.forEach(item => {
    printer.tableCustom([
      { text: item.name, align: "LEFT", width: 0.6 },
      { text: item.quantity.toString(), align: "CENTER", width: 0.15 },
      { text: item.price.toFixed(2), align: "RIGHT", width: 0.25 }
    ]);
  });

  printer.drawLine();

  printer.alignRight();
  printer.bold(true);
  printer.println(`Total: ${DUMMY_RECEIPT_DATA.total.toFixed(2)} THB`);
  printer.bold(false);
  printer.println(`Paid by: ${DUMMY_RECEIPT_DATA.paymentMethod}`);

  printer.newLine();
  printer.alignCenter();
  printer.println("--- END OF TEST ---");
  printer.newLine();
  printer.newLine();
  printer.newLine();

//   printer.cut();

  // Write to buffer
  const buffer = printer.getBuffer();

  // Save temp file
  const tempDir = path.join(__dirname, 'temp');
  if (!fs.existsSync(tempDir)) fs.mkdirSync(tempDir);
  const tempFilePath = path.join(tempDir, `test-receipt-${Date.now()}.bin`);
  fs.writeFileSync(tempFilePath, buffer);
  console.log(`[DEBUG] Created temp print file: ${tempFilePath}`);

  // Print via lp
  const printCommand = `lp -d "${PRINTER_NAME}" -o raw "${tempFilePath}"`;
  console.log(`[DEBUG] Running: ${printCommand}`);

  return new Promise((resolve, reject) => {
    exec(printCommand, (error, stdout, stderr) => {
      fs.unlinkSync(tempFilePath); // always remove file
      if (error) {
        console.error(`[ERROR] Print error: ${stderr}`);
        return reject(new Error(stderr));
      }
      console.log(`[✅ SUCCESS] Print job sent.`);
      resolve(stdout);
    });
  });
}

testPrint().catch(err => {
  console.error("Print test failed:", err);
});
