// src/renderer.js

// Get references to all the UI elements we need to update
const logContainer = document.getElementById('logContainer');
const readerStatusEl = document.getElementById('readerStatus');
const cardStatusEl = document.getElementById('cardStatus');
const connectionStatusEl = document.getElementById('connectionStatus');

/**
 * Adds a new log message to the log container.
 * @param {string} message The message to log.
 */
function addLog(message) {
    // To prevent the log from growing indefinitely, remove the oldest log entry if we exceed 200 lines.
    if (logContainer.children.length > 200) {
        logContainer.removeChild(logContainer.firstChild);
    }
    const p = document.createElement('p');
    // Format the log message with a timestamp.
    p.innerHTML = `<span class="text-gray-500">[${new Date().toLocaleTimeString()}]</span> ${message}`;
    logContainer.appendChild(p);
    // Automatically scroll to the bottom of the log container.
    logContainer.scrollTop = logContainer.scrollHeight;
}

/**
 * Updates a status element with new text and a colored dot.
 * @param {HTMLElement} element The HTML element to update.
 * @param {string} text The new status text.
 * @param {string} color The Tailwind CSS color name (e.g., 'green', 'red', 'gray', 'yellow').
 */
function setStatus(element, text, color) {
    let pulseClass = color === 'yellow' ? 'animate-pulse' : '';
    element.innerHTML = `<span class="status-dot bg-${color}-500 ${pulseClass} mr-2"></span>${text}`;
}

// --- Event Listeners for data from the Main Process ---

// Listen for log messages from main.js
window.electronAPI.onLog((message) => {
    addLog(message);
});

// Listen for status updates from main.js
window.electronAPI.onStatusUpdate((data) => {
    // ไม่จำเป็นต้อง log ซ้ำที่นี่ เพราะ main.js ส่ง log มาแล้ว
    // addLog(`Status Update: ${data.type} - ${data.status}`);
    
    switch (data.type) {
        case 'connection':
            // --- ส่วนที่เพิ่มเข้ามา ---
            if (data.status === 'connected') {
                setStatus(connectionStatusEl, 'เชื่อมต่อสำเร็จ', 'green');
            } else if (data.status === 'connecting') {
                setStatus(connectionStatusEl, 'กำลังเชื่อมต่อ...', 'yellow');
            } else if (data.status === 'error') {
                setStatus(connectionStatusEl, `เชื่อมต่อล้มเหลว`, 'red');
            } else { // disconnected
                setStatus(connectionStatusEl, 'ตัดการเชื่อมต่อ', 'red');
            }
            break;
            
        case 'reader':
            if (data.status === 'connected') {
                if (data.name) {
                    setStatus(readerStatusEl, `เชื่อมต่อแล้ว (${data.name})`, 'green');
                } else {
                    setStatus(readerStatusEl, 'เชื่อมต่อแล้ว', 'green');
                }
            } else if (data.status === 'disconnected') {
                if (data.name) {
                    setStatus(readerStatusEl, `อุปกรณ์ถูกถอด (${data.name})`, 'red');
                } else {
                    setStatus(readerStatusEl, 'ไม่พบอุปกรณ์', 'gray');
                }
                // When reader is disconnected, also reset card status.
                setStatus(cardStatusEl, 'ไม่มีบัตร', 'gray');
            } else if (data.status === 'error' || data.status === 'fatal_error') {
                setStatus(readerStatusEl, 'เกิดข้อผิดพลาด', 'red');
                setStatus(cardStatusEl, 'ไม่มีบัตร', 'gray');
            }
            break;

        case 'card':
            if (data.status === 'reading') {
                setStatus(cardStatusEl, 'กำลังอ่านข้อมูล...', 'yellow');
            } else if (data.status === 'removed') {
                setStatus(cardStatusEl, 'นำบัตรออกแล้ว', 'gray');
            } else if (data.status === 'read_success') {
                setStatus(cardStatusEl, 'อ่านข้อมูลสำเร็จ', 'green');
                // You can add code here to display card data in another part of the UI.
                addLog('ข้อมูลบัตร: ' + JSON.stringify(data.data, null, 2));
            } else if (data.status === 'read_error') {
                setStatus(cardStatusEl, 'อ่านบัตรล้มเหลว', 'red');
            }
            break;
    }
});

// --- Settings Modal Functionality ---
const settingsModal = document.getElementById('settingsModal');
const settingsBtn = document.getElementById('settingsBtn');
const closeSettingsBtn = document.getElementById('closeSettingsBtn');
const cancelSettingsBtn = document.getElementById('cancelSettingsBtn');
const settingsForm = document.getElementById('settingsForm');
const environmentSelect = document.getElementById('environmentSelect');

// Form fields
const clinicIdField = document.getElementById('clinicId');
const stationIdField = document.getElementById('stationId');
const vpsUrlField = document.getElementById('vpsUrl');
const apiKeyField = document.getElementById('apiKey');

let environmentPresets = {};

// Load environment presets and current config
async function loadSettings() {
    try {
        environmentPresets = await window.electronAPI.getEnvironmentPresets();
        const config = await window.electronAPI.getConfig();

        // Populate form with current config
        environmentSelect.value = config.environment || 'local';
        clinicIdField.value = config.clinicId || '';
        stationIdField.value = config.stationId || '';
        vpsUrlField.value = config.vpsUrl || '';
        apiKeyField.value = config.apiKey || '';

        addLog('[INFO] โหลดการตั้งค่าสำเร็จ');
    } catch (error) {
        addLog(`[ERROR] ไม่สามารถโหลดการตั้งค่าได้: ${error.message}`);
    }
}

// Handle environment selection change
environmentSelect.addEventListener('change', () => {
    const selectedEnv = environmentSelect.value;
    if (selectedEnv !== 'custom' && environmentPresets[selectedEnv]) {
        const preset = environmentPresets[selectedEnv];
        vpsUrlField.value = preset.vpsUrl;
        apiKeyField.value = preset.apiKey;
    }
});

// Show settings modal
settingsBtn.addEventListener('click', () => {
    loadSettings();
    settingsModal.classList.remove('hidden');
});

// Hide settings modal
function hideSettingsModal() {
    settingsModal.classList.add('hidden');
}

closeSettingsBtn.addEventListener('click', hideSettingsModal);
cancelSettingsBtn.addEventListener('click', hideSettingsModal);

// Close modal when clicking outside
settingsModal.addEventListener('click', (e) => {
    if (e.target === settingsModal) {
        hideSettingsModal();
    }
});

// Handle form submission
settingsForm.addEventListener('submit', async (e) => {
    e.preventDefault();

    const config = {
        environment: environmentSelect.value,
        clinicId: clinicIdField.value.trim(),
        stationId: stationIdField.value.trim(),
        vpsUrl: vpsUrlField.value.trim(),
        apiKey: apiKeyField.value.trim()
    };

    try {
        await window.electronAPI.saveConfig(config);
        addLog('[INFO] บันทึกการตั้งค่าสำเร็จ');
        hideSettingsModal();
    } catch (error) {
        addLog(`[ERROR] ไม่สามารถบันทึกการตั้งค่าได้: ${error.message}`);
    }
});

// Log that the UI is ready.
addLog('UI Renderer พร้อมทำงาน');

// Test function to demonstrate scrolling (for development/testing purposes)
function testScrolling() {
    for (let i = 1; i <= 20; i++) {
        setTimeout(() => {
            addLog(`[TEST] ข้อความทดสอบการเลื่อน #${i} - Lorem ipsum dolor sit amet, consectetur adipiscing elit.`);
        }, i * 100);
    }
}

// Add a keyboard shortcut to test scrolling (Ctrl+T)
document.addEventListener('keydown', (event) => {
    if (event.ctrlKey && event.key === 't') {
        event.preventDefault();
        testScrolling();
        addLog('[INFO] เริ่มทดสอบการเลื่อน (กด Ctrl+T อีกครั้งเพื่อทดสอบซ้ำ)');
    }
});
