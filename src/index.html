<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <title>Hompok Clinic Bridge</title>
    <!-- Content-Security-Policy to allow loading TailwindCSS and local scripts -->
    <meta http-equiv="Content-Security-Policy" content="script-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com;">
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* It's recommended to link a Google Font stylesheet for Thai fonts if needed */
        /* @import url('https://fonts.googleapis.com/css2?family=Sarabun:wght@400;700&display=swap'); */
        body { 
            font-family: 'Sarabun', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
        }
        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            flex-shrink: 0; /* Prevents the dot from shrinking */
        }
        #logContainer {
            max-height: 400px; /* Set a maximum height to enable scrolling */
            scroll-behavior: smooth; /* Smooth scrolling */
        }
        /* Responsive max-height for different screen sizes */
        @media (max-height: 600px) {
            #logContainer {
                max-height: 250px;
            }
        }
        @media (min-height: 800px) {
            #logContainer {
                max-height: 500px;
            }
        }
        /* Custom scrollbar styling for better visibility */
        #logContainer::-webkit-scrollbar {
            width: 8px;
        }
        #logContainer::-webkit-scrollbar-track {
            background: #374151; /* Darker gray for track */
            border-radius: 4px;
        }
        #logContainer::-webkit-scrollbar-thumb {
            background: #6b7280; /* Light gray for thumb */
            border-radius: 4px;
        }
        #logContainer::-webkit-scrollbar-thumb:hover {
            background: #9ca3af; /* Lighter gray on hover */
        }
    </style>
</head>
<body class="bg-slate-100 h-screen flex flex-col">
    <header class="bg-white shadow p-4 flex items-center justify-between z-10">
        <div class="flex items-center">
            <img src="./logo.png" alt="Logo" class="h-10 w-10 mr-4">
            <div>
                <h1 class="text-xl font-bold text-slate-800">Hompok Clinic Bridge</h1>
                <p id="connectionStatus" class="text-sm text-slate-500 flex items-center">
                    <span class="status-dot bg-red-500 mr-2"></span>Disconnected
                </p>
            </div>
        </div>
    </header>

    <main class="flex-grow p-4 grid grid-cols-1 md:grid-cols-3 gap-4 overflow-hidden min-h-0">
        <!-- Status Panel -->
        <div class="md:col-span-1 bg-white rounded-lg shadow p-4 flex flex-col gap-4">
            <div>
                <h3 class="font-bold text-slate-700">สถานะเครื่องอ่านบัตร</h3>
                <p id="readerStatus" class="text-slate-500 flex items-center">
                    <span class="status-dot bg-gray-400 mr-2"></span>ไม่พบอุปกรณ์
                </p>
            </div>
            <div>
                <h3 class="font-bold text-slate-700">สถานะบัตร</h3>
                <p id="cardStatus" class="text-slate-500 flex items-center">
                    <span class="status-dot bg-gray-400 mr-2"></span>ไม่มีบัตร
                </p>
            </div>
        </div>

        <!-- Log Panel -->
        <div class="md:col-span-2 bg-white rounded-lg shadow p-4 flex flex-col h-full">
            <h3 class="font-bold text-slate-700 mb-2">Log การทำงาน</h3>
            <div id="logContainer" class="bg-slate-900 text-white font-mono text-xs p-3 rounded-md flex-grow overflow-y-auto min-h-0">
                <p class="text-gray-400">กำลังรอรับข้อมูล...</p>
            </div>
        </div>
    </main>

    <!-- The renderer script is linked here -->
    <script src="./renderer.js"></script>
</body>
</html>
