// generate-icon.js - Generate proper Windows icon from PNG
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('ACTSE Hardware Bridge - Icon Generator');
console.log('=====================================');

const logoPath = path.join(__dirname, 'src', 'logo.png');
const buildDir = path.join(__dirname, 'build');
const iconPath = path.join(buildDir, 'icon.ico');

// Create build directory if it doesn't exist
if (!fs.existsSync(buildDir)) {
    fs.mkdirSync(buildDir, { recursive: true });
    console.log('✓ Created build directory');
}

if (!fs.existsSync(logoPath)) {
    console.log('✗ src/logo.png not found');
    console.log('\nPlease ensure you have a logo.png file in the src/ directory');
    process.exit(1);
}

console.log('✓ Found src/logo.png');

// Check if ImageMagick is available
try {
    execSync('magick -version', { stdio: 'ignore' });
    console.log('✓ ImageMagick is available');
    
    try {
        // Generate ICO file with multiple sizes including 256x256
        const command = `magick "${logoPath}" -resize 256x256 -define icon:auto-resize=256,128,64,48,32,16 "${iconPath}"`;
        execSync(command, { stdio: 'inherit' });
        console.log('✓ Generated build/icon.ico with ImageMagick');
        
        // Verify the icon was created
        if (fs.existsSync(iconPath)) {
            const stats = fs.statSync(iconPath);
            console.log(`✓ Icon file size: ${stats.size} bytes`);
            console.log('\n✅ Icon generation completed successfully!');
            console.log('\nYou can now run: npm run build:win');
        } else {
            console.log('✗ Icon file was not created');
        }
        
    } catch (error) {
        console.log('✗ Failed to generate icon with ImageMagick');
        console.log('Error:', error.message);
        fallbackMethod();
    }
    
} catch (error) {
    console.log('⚠ ImageMagick not found, using fallback method');
    fallbackMethod();
}

function fallbackMethod() {
    console.log('\n--- Fallback Method ---');
    console.log('Since ImageMagick is not available, please create the icon manually:');
    console.log('\n1. Online Converter (Recommended):');
    console.log('   - Go to https://convertio.co/png-ico/');
    console.log('   - Upload your src/logo.png file');
    console.log('   - In advanced settings, select multiple sizes:');
    console.log('     ✓ 16x16, 32x32, 48x48, 64x64, 128x128, 256x256');
    console.log('   - Download the converted file');
    console.log('   - Save it as build/icon.ico');
    
    console.log('\n2. Using GIMP (Free):');
    console.log('   - Open src/logo.png in GIMP');
    console.log('   - Image > Scale Image > Set to 256x256 pixels');
    console.log('   - File > Export As > build/icon.ico');
    console.log('   - In the ICO export dialog, check these sizes:');
    console.log('     ✓ 16x16, 32x32, 48x48, 64x64, 128x128, 256x256');
    
    console.log('\n3. Using online tool:');
    console.log('   - https://icoconvert.com/');
    console.log('   - Upload PNG, select "Custom sizes"');
    console.log('   - Choose: 16, 32, 48, 64, 128, 256');
    console.log('   - Download and save as build/icon.ico');
    
    console.log('\n4. Install ImageMagick (for future use):');
    console.log('   - Windows: https://imagemagick.org/script/download.php#windows');
    console.log('   - macOS: brew install imagemagick');
    console.log('   - Linux: sudo apt-get install imagemagick');
    
    console.log('\n⚠ IMPORTANT: The icon MUST include a 256x256 size for Windows installer!');
    console.log('\nAfter creating build/icon.ico, run: npm run build:win');
}

// Check if we already have a proper icon
if (fs.existsSync(iconPath)) {
    const stats = fs.statSync(iconPath);
    console.log(`\n✓ Existing icon found: build/icon.ico (${stats.size} bytes)`);
    console.log('If the build still fails, the icon might not have the required 256x256 size.');
    console.log('Please regenerate it using one of the methods above.');
}
