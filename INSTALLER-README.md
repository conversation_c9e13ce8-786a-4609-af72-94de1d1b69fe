# ACTSE Hardware Bridge - Windows Installer Guide

## Overview
This guide explains how to create a Windows installer for the ACTSE Hardware Bridge application with professional setup wizard and auto-startup functionality.

## Prerequisites

1. **Node.js and npm** installed
2. **Windows development environment** (for building Windows installer)
3. **Logo icon file** (logo.ico) in the src/ directory

## Setup Steps

### 1. Install Dependencies
```bash
npm install
```

### 2. Create Windows Icon
You need to convert your PNG logo to ICO format:

**Option A: Online Converter (Easiest)**
1. Go to https://convertio.co/png-ico/
2. Upload `src/logo.png`
3. Download the converted file
4. Save as `src/logo.ico`

**Option B: Using ImageMagick**
```bash
magick src/logo.png -define icon:auto-resize=256,128,64,48,32,16 src/logo.ico
```

**Option C: Run helper script**
```bash
node create-icon.js
```

### 3. Build the Installer

**Build for all Windows architectures:**
```bash
npm run build:win
```

**Build for specific architecture:**
```bash
npm run build:win32  # 32-bit Windows
npm run build:win64  # 64-bit Windows
```

**Quick distribution build:**
```bash
npm run dist
```

## Installer Features

### ✅ Professional Setup Wizard
- Custom installer with ACTSE branding
- Installation directory selection
- Start menu and desktop shortcuts
- Proper uninstaller

### ✅ Auto-Startup Configuration
- Option to start with Windows
- Registry-based startup (reliable)
- Backup shortcut method
- User can enable/disable during installation

### ✅ Windows Integration
- Add/Remove Programs entry
- Proper file associations
- Administrator privileges when needed
- Clean uninstallation

## Installer Output

The installer will be created in the `dist/` directory:
```
dist/
├── ACTSE-Hardware-Bridge-Setup-2.0.0.exe  # Main installer
├── win-unpacked/                           # Unpacked files (for testing)
└── latest.yml                             # Update metadata
```

## Installation Process

1. **Welcome Screen**: Shows ACTSE Hardware Bridge branding
2. **License Agreement**: Standard software license
3. **Installation Directory**: User can choose install location (default: Program Files)
4. **Startup Options**: Checkbox to start with Windows
5. **Start Menu Shortcuts**: Option to create shortcuts
6. **Desktop Shortcut**: Option to create desktop icon
7. **Installation Progress**: Shows installation progress
8. **Completion**: Option to launch application immediately

## Auto-Startup Implementation

The installer implements two methods for auto-startup:

### Method 1: Windows Registry (Primary)
```
HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Run
"ACTSE Hardware Bridge" = "C:\Program Files\ACTSE Hardware Bridge\ACTSE Hardware Bridge.exe --hidden"
```

### Method 2: Startup Folder (Backup)
Creates shortcut in:
```
%APPDATA%\Microsoft\Windows\Start Menu\Programs\Startup\
```

## Uninstallation

The uninstaller will:
- Remove all application files
- Clean up registry entries
- Remove startup entries
- Delete shortcuts
- Remove from Add/Remove Programs

## Testing the Installer

1. **Build the installer**: `npm run build:win`
2. **Test installation**: Run the .exe file
3. **Verify auto-startup**: Restart Windows and check if app starts
4. **Test uninstallation**: Use Add/Remove Programs or uninstaller

## Troubleshooting

### Common Issues:

**"logo.ico not found"**
- Convert your PNG logo to ICO format
- Place in `src/logo.ico`

**"electron-builder not found"**
- Run `npm install --save-dev electron-builder`

**"Build failed"**
- Check Node.js version compatibility
- Ensure all dependencies are installed
- Run `npm run rebuild`

**Auto-startup not working**
- Check Windows startup settings
- Verify registry entries
- Run installer as administrator

## Distribution

The final installer (`ACTSE-Hardware-Bridge-Setup-2.0.0.exe`) can be:
- Distributed to clients
- Hosted on your website
- Sent via email
- Deployed through software management systems

## Security Notes

- The installer requests administrator privileges for:
  - Installing to Program Files
  - Writing to system registry
  - Creating system-wide shortcuts
- Users can install to user directory without admin rights
- Auto-startup uses user-level registry (no admin required)

## Support

For installation issues:
1. Check Windows compatibility (Windows 7+)
2. Verify user permissions
3. Check antivirus software interference
4. Review Windows Event Logs for errors
