{"name": "actse-hardware-bridge", "version": "2.0.0", "description": "Desktop Hardware Bridge for ACTSE Clinic Applications", "main": "main.js", "scripts": {"start": "electron .", "build": "electron-builder", "build:win": "electron-builder --win", "build:win32": "electron-builder --win --ia32", "build:win64": "electron-builder --win --x64", "dist": "npm run build:win", "rebuild": "electron-rebuild", "postinstall": "electron-rebuild"}, "author": "<PERSON><PERSON><PERSON>", "license": "ISC", "dependencies": {"axios": "^1.7.2", "electron-store": "^8.2.0", "iconv-lite": "^0.6.3", "node-thermal-printer": "^4.5.0", "smartcard": "^1.0.46", "socket.io-client": "^4.7.5"}, "devDependencies": {"@electron/rebuild": "^3.4.0", "electron": "^22.3.27", "electron-builder": "^24.13.3"}, "build": {"appId": "com.actse.hardwarebridge", "productName": "ACTSE Hardware Bridge", "copyright": "Copyright © 2024 ACTSE", "directories": {"buildResources": "build"}, "files": ["main.js", "preload.js", "src/**/*", "node_modules/**/*", "!node_modules/.cache", "!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}"], "extraResources": [{"from": "./th-address-db.json", "to": "th-address-db.json"}], "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}], "icon": "src/logo.ico", "requestedExecutionLevel": "requireAdministrator", "publisherName": "ACTSE"}, "nsis": {"oneClick": false, "allowElevation": true, "allowToChangeInstallationDirectory": true, "installerIcon": "src/logo.ico", "uninstallerIcon": "src/logo.ico", "installerHeaderIcon": "src/logo.ico", "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "ACTSE Hardware Bridge", "runAfterFinish": true, "menuCategory": "ACTSE", "artifactName": "ACTSE-Hardware-Bridge-Setup-${version}.${ext}", "deleteAppDataOnUninstall": false, "include": "installer.nsh"}}}