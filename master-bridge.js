// master-bridge.js (Version 4.2 - TIS-620 Encoding Fix)

const { io } = require('socket.io-client');
const { Devices, CommandApdu } = require('smartcard');
const { Printer: ThermalPrinter, PrinterTypes } = require('node-thermal-printer');
const axios = require('axios');
const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const iconv = require('iconv-lite'); // *** เพิ่ม: ไลบรารีสำหรับถอดรหัส TIS-620 ***

// --- 1. อ่านค่า Config และตั้งค่าการเชื่อมต่อ ---
let config;
try {
    const exeDir = path.dirname(process.execPath);
    const configPath = path.join(exeDir, 'config.json');
    console.log(`[INFO] กำลังอ่านไฟล์คอนฟิกจาก: ${configPath}`);

    if (!fs.existsSync(configPath)) throw new Error("ไม่พบไฟล์ config.json! กรุณาวางไฟล์ config.json ไว้ในโฟลเดอร์เดียวกับ Executable");
    config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
} catch (error) {
    console.error("เกิดข้อผิดพลาดในการอ่านไฟล์ config.json:", error.message);
    setTimeout(() => process.exit(1), 15000);
    return;
}

const { clinicId, stationId, vpsUrl, apiKey } = config;
if (!clinicId || !stationId || !vpsUrl || !apiKey) {
    console.error("ข้อมูลใน config.json ไม่ครบถ้วน (ต้องการ clinicId, stationId, vpsUrl, apiKey)");
    setTimeout(() => process.exit(1), 15000);
    return;
}

const BRIDGE_ID = `${clinicId}::${stationId}`;
let socket = null;

let addressDatabase = [];
try {
    console.log('[INFO] กำลังโหลดฐานข้อมูลที่อยู่จากไฟล์...');
    const dbPath = path.join(__dirname, 'th-address-db.json');
    addressDatabase = JSON.parse(fs.readFileSync(dbPath, 'utf8'));
    console.log('[INFO] โหลดฐานข้อมูลที่อยู่สำเร็จ!');
} catch (error) {
    console.warn(`[WARN] ไม่พบไฟล์ th-address-db.json หรือไฟล์เสียหาย, จะไม่สามารถค้นหารหัสไปรษณีย์ได้: ${error.message}`);
}

// ... (ส่วน connectToVps, sendMessageToVps, handlePrintCommand, print... เหมือนเดิม) ...

function connectToVps() {
    console.log(`====================================================`);
    console.log(`  Hardware Bridge ID: [${BRIDGE_ID}]`);
    console.log(`  กำลังเชื่อมต่อกับ Server: ${vpsUrl}`);
    console.log(`====================================================`);

    socket = io(vpsUrl, {
        reconnectionDelayMax: 10000,
        auth: {
            apiKey: apiKey,
            bridgeId: BRIDGE_ID
        }
    });

    socket.on('connect', () => {
        console.log(`[INFO] เชื่อมต่อกับ VPS สำเร็จ! (Socket ID: ${socket.id})`);
    });

    socket.on('printCommand', (command) => {
        console.log(`[INFO] ได้รับคำสั่ง 'printCommand' จาก VPS:`, command);
        handlePrintCommand(command);
    });

    socket.on('disconnect', (reason) => {
        console.log(`[WARN] การเชื่อมต่อกับ VPS ถูกตัดขาด! Reason: ${reason}`);
    });

    socket.on('connect_error', (err) => {
        console.error(`[ERROR] เกิดข้อผิดพลาดในการเชื่อมต่อ: ${err.message}`);
    });
}

function sendMessageToVps(status, payload) {
    if (socket && socket.connected) {
        const message = { source: BRIDGE_ID, status, payload };
        socket.emit('bridgeEvent', message);
    }
}

async function handlePrintCommand(payload) {
    const { printerName, jobType, data } = payload;
    try {
        if (jobType === 'receipt') await printReceipt(printerName, data);
        else if (jobType === 'label') await printLabel(printerName, data);
        else if (jobType === 'document') await printDocument(printerName, data);
        else throw new Error(`ไม่รู้จักประเภทงานพิมพ์: ${jobType}`);
        sendMessageToVps('print_success', { jobType, printerName });
    } catch (error) {
        console.error(`[ERROR] พิมพ์ [${jobType}] ล้มเหลว:`, error);
        sendMessageToVps('print_error', { jobType, printerName, error: error.message });
    }
}

async function printReceipt(printerName, data) { console.log(`กำลังพิมพ์ใบเสร็จไปยัง ${printerName}...`); }
async function printLabel(printerName, data) { console.log(`กำลังพิมพ์ฉลากไปยัง ${printerName}...`); }
async function printDocument(printerName, data) {
    const { fileUrl } = data;
    if (!fileUrl) throw new Error("ไม่พบ URL ของไฟล์ที่จะพิมพ์");
    const tempDir = require('os').tmpdir();
    const fileName = `printjob-${Date.now()}.pdf`;
    const localFilePath = path.join(tempDir, fileName);
    console.log(`กำลังดาวน์โหลดไฟล์จาก: ${fileUrl}`);
    const response = await axios({ url: fileUrl, method: 'GET', responseType: 'stream' });
    const writer = fs.createWriteStream(localFilePath);
    response.data.pipe(writer);
    await new Promise((resolve, reject) => {
        writer.on('finish', resolve);
        writer.on('error', reject);
    });
    console.log(`กำลังส่งไฟล์ไปพิมพ์ที่เครื่อง: ${printerName}`);
    const printCommand = process.platform === 'win32'
        ? `print /d:"${printerName}" "${localFilePath}"`
        : `lp -d "${printerName}" "${localFilePath}"`;
    return new Promise((resolve, reject) => {
        exec(printCommand, (error, stdout, stderr) => {
            fs.unlinkSync(localFilePath);
            if (error) {
                console.error(`เกิดข้อผิดพลาดในการพิมพ์เอกสาร: ${stderr}`);
                return reject(new Error(stderr));
            }
            console.log(`ส่งไฟล์ไปพิมพ์สำเร็จ: ${stdout}`);
            resolve(stdout);
        });
    });
}

// --- 3. ส่วนจัดการ Smart Card ---
const devices = new Devices();

devices.on('device-activated', event => {
    const { device } = event;
    console.log(`[INFO] พบเครื่องอ่านบัตร: ${device.name}`);
    sendMessageToVps('reader_connected', { readerName: device.name });

    device.on('card-inserted', async event => {
        const { card } = event;
        console.log(`[INFO] พบบัตรในเครื่องอ่าน ${device.name}`);
        sendMessageToVps('reading', { message: 'กำลังอ่านข้อมูล...' });
        try {
            await new Promise(resolve => setTimeout(resolve, 100));
            const cardData = await readThaiIDData(card);
            console.log('[DATA] ข้อมูลที่อ่านได้:', cardData);
            sendMessageToVps('card_read_success', { data: cardData });
        } catch (error) {
            console.error(`[ERROR] อ่านบัตรล้มเหลว:`, error);
            sendMessageToVps('card_read_error', { message: `อ่านบัตรล้มเหลว: ${error.message}` });
        }
    });

    device.on('card-removed', () => {
        console.log(`[INFO] นำบัตรออกจากเครื่องอ่าน ${device.name}`);
        sendMessageToVps('card_removed', {});
    });

    device.on('error', err => {
        console.error(`[ERROR] Reader Error (${device.name}):`, err.message);
    });
});

devices.on('device-deactivated', event => {
    console.log(`[WARN] เครื่องอ่าน ${event.device.name} ถูกถอดออก`);
    sendMessageToVps('reader_disconnected', { readerName: event.device.name });
});

async function readThaiIDData(card) {
    console.log('[DEBUG] เริ่มกระบวนการอ่านข้อมูลบัตร');

    const issueCommand = (commandBytes) => {
        return new Promise((resolve, reject) => {
            const command = new CommandApdu({ bytes: commandBytes });
            card.issueCommand(command, (err, response) => {
                if (err) return reject(new Error(err));
                if (!response) return reject(new Error('การตอบกลับจากบัตรไม่ถูกต้อง'));
                resolve(response);
            });
        });
    };

    const getData = async (requestApdu) => {
        let response = await issueCommand(requestApdu);
        let sw1 = response.slice(-2, -1).toString('hex');
        let sw2 = response.slice(-1).toString('hex');
        if (sw1 === '61') {
            const getResponseLength = parseInt(sw2, 16);
            const getResponseApdu = [0x00, 0xC0, 0x00, 0x00, getResponseLength];
            response = await issueCommand(getResponseApdu);
            sw1 = response.slice(-2, -1).toString('hex');
            sw2 = response.slice(-1).toString('hex');
        }
        if (sw1 === '90' && sw2 === '00') {
            return response.slice(0, -2);
        } else {
            throw new Error(`คำสั่งล้มเหลว (SW=${sw1}${sw2})`);
        }
    };

    await getData([0x00, 0xA4, 0x04, 0x00, 0x08, 0xA0, 0x00, 0x00, 0x00, 0x54, 0x48, 0x00, 0x01]);
    console.log('[DEBUG] เลือกแอปเพล็ตบัตรประชาชนสำเร็จ');

    // *** ส่วนที่แก้ไข: ใช้ iconv-lite ในการถอดรหัส ***
    const formatAsDate = (rawDate) => `${rawDate.substring(6, 8)}/${rawDate.substring(4, 6)}/${rawDate.substring(0, 4)}`;
    const cleanAndTrim = (buffer) => iconv.decode(buffer, 'tis-620').replace(/\x00/g, '').trim();

    const citizenId = cleanAndTrim(await getData([0x80, 0xb0, 0x00, 0x04, 0x02, 0x00, 0x0d]));
    const fullNameTHRaw = cleanAndTrim(await getData([0x80, 0xb0, 0x00, 0x11, 0x02, 0x00, 0x64]));
    const fullNameENRaw = cleanAndTrim(await getData([0x80, 0xb0, 0x00, 0x75, 0x02, 0x00, 0x64]));
    const dobRaw = cleanAndTrim(await getData([0x80, 0xb0, 0x00, 0xD9, 0x02, 0x00, 0x08]));
    const genderRaw = cleanAndTrim(await getData([0x80, 0xb0, 0x00, 0xE1, 0x02, 0x00, 0x01]));
    const issuerRaw = cleanAndTrim(await getData([0x80, 0xb0, 0x00, 0xF6, 0x02, 0x00, 0x64]));
    const issueDateRaw = cleanAndTrim(await getData([0x80, 0xb0, 0x01, 0x67, 0x02, 0x00, 0x08]));
    const expireDateRaw = cleanAndTrim(await getData([0x80, 0xB0, 0x01, 0x6F, 0x02, 0x00, 0x08]));
    const addressRaw = cleanAndTrim(await getData([0x80, 0xb0, 0x15, 0x79, 0x02, 0x00, 0xA0]));
    
    let religion = 'ไม่ระบุ';
    try {
        const religionRaw = cleanAndTrim(await getData([0x80, 0xb0, 0x01, 0x77, 0x02, 0x00, 0x02]));
        religion = religionRaw.trim();
    } catch (e) {
        console.log('[DEBUG] ไม่พบข้อมูลศาสนาบนบัตร หรืออ่านไม่สำเร็จ');
    }

    let photoBuffer = Buffer.alloc(0);
    const photoCommands = [
        [0x80,0xb0,0x01,0x7B,0x02,0x00,0xff], [0x80,0xb0,0x02,0x7A,0x02,0x00,0xff],
        [0x80,0xb0,0x03,0x79,0x02,0x00,0xff], [0x80,0xb0,0x04,0x78,0x02,0x00,0xff],
        [0x80,0xb0,0x05,0x77,0x02,0x00,0xff], [0x80,0xb0,0x06,0x76,0x02,0x00,0xff],
        [0x80,0xb0,0x07,0x75,0x02,0x00,0xff], [0x80,0xb0,0x08,0x74,0x02,0x00,0xff],
        [0x80,0xb0,0x09,0x73,0x02,0x00,0xff], [0x80,0xb0,0x0A,0x72,0x02,0x00,0xff],
        [0x80,0xb0,0x0B,0x71,0x02,0x00,0xff], [0x80,0xb0,0x0C,0x70,0x02,0x00,0xff],
        [0x80,0xb0,0x0D,0x6F,0x02,0x00,0xff], [0x80,0xb0,0x0E,0x6E,0x02,0x00,0xff],
        [0x80,0xb0,0x0F,0x6D,0x02,0x00,0xff], [0x80,0xb0,0x10,0x6C,0x02,0x00,0xff],
        [0x80,0xb0,0x11,0x6B,0x02,0x00,0xff], [0x80,0xb0,0x12,0x6A,0x02,0x00,0xff],
        [0x80,0xb0,0x13,0x69,0x02,0x00,0xff], [0x80,0xb0,0x14,0x68,0x02,0x00,0xff],
    ];

    for (const cmd of photoCommands) {
        try {
            const segment = await getData(cmd);
            photoBuffer = Buffer.concat([photoBuffer, segment]);
        } catch (e) {
            console.log(`[DEBUG] อ่านรูปภาพจบ (อาจไม่ครบ 20 ส่วน)`);
            break;
        }
    }
    const photoBase64 = photoBuffer.length > 0 ? `data:image/jpeg;base64,${photoBuffer.toString('base64')}` : null;

    const [titleTH, firstNameTH, ...lastNameTHParts] = fullNameTHRaw.split('#');
    const [titleEN, firstNameEN, ...lastNameENParts] = fullNameENRaw.split('#');
    const addressParts = addressRaw.split('#');
    
    const calculateAge = (startDateString, endDateString = null) => {
        const startYearBE = parseInt(startDateString.substring(0, 4), 10);
        const startMonth = parseInt(startDateString.substring(4, 6), 10) - 1;
        const startDay = parseInt(startDateString.substring(6, 8), 10);
        const startDate = new Date(startYearBE - 543, startMonth, startDay);
        let endDate;
        if (endDateString) {
            const endYearBE = parseInt(endDateString.substring(0, 4), 10);
            const endMonth = parseInt(endDateString.substring(4, 6), 10) - 1;
            const endDay = parseInt(endDateString.substring(6, 8), 10);
            endDate = new Date(endYearBE - 543, endMonth, endDay);
        } else {
            endDate = new Date();
        }
        if (startDate > endDate) return "0 ปี 0 เดือน 0 วัน";
        let years = endDate.getFullYear() - startDate.getFullYear();
        let months = endDate.getMonth() - startDate.getMonth();
        let days = endDate.getDate() - startDate.getDate();
        if (days < 0) {
            months--;
            const lastDayOfPrevMonth = new Date(endDate.getFullYear(), endDate.getMonth(), 0).getDate();
            days += lastDayOfPrevMonth;
        }
        if (months < 0) {
            years--;
            months += 12;
        }
        return `${years} ปี ${months} เดือน ${days} วัน`;
    };
    
    const province = (addressParts[7] || '').replace(/^(จังหวัด|จ\.)/, '').trim();
    const district = (addressParts[6] || '').replace(/^(อำเภอ|อ\.)/, '').trim();
    const subDistrict = (addressParts[5] || '').replace(/^(ตำบล|ต\.)/, '').trim();
    
    let postcode = '';
    if (addressDatabase.length > 0) {
        const provinceData = addressDatabase.find(p => p.name_th === province);
        if (provinceData) {
            const districtData = provinceData.amphure.find(d => d.name_th === district);
            if (districtData) {
                const subDistrictData = districtData.tambon.find(s => s.name_th === subDistrict);
                if (subDistrictData) {
                    postcode = subDistrictData.zip_code.toString();
                }
            }
        }
    }
    console.log(`[DEBUG] ค้นหารหัสไปรษณีย์สำหรับ ${subDistrict}, ${district}, ${province}: ${postcode || 'ไม่พบ'}`);

    return {
        citizenId,
        titleTH: titleTH.trim(),
        firstNameTH: firstNameTH.trim(),
        lastNameTH: lastNameTHParts.join(' ').trim(),
        titleEN: titleEN.trim(),
        firstNameEN: firstNameEN.trim(),
        lastNameEN: lastNameENParts.join(' ').trim(),
        dob: formatAsDate(dobRaw),
        gender: genderRaw === '1' ? 'ชาย' : (genderRaw === '2' ? 'หญิง' : 'ไม่ระบุ'),
        issuer: issuerRaw.trim(),
        issueDate: formatAsDate(issueDateRaw),
        expireDate: formatAsDate(expireDateRaw),
        religion: religion,
        addressFull: addressRaw.replace(/#+/g, ' ').trim(),
        address: {
            houseNo: (addressParts[0] || '').trim(),
            villageNo: (addressParts[1] || '').trim(),
            lane: (addressParts[2] || '').trim(),
            road: (addressParts[3] || '').trim(),
            building: (addressParts[4] || '').trim(),
            subDistrict,
            district,
            province,
            postcode,
        },
        currentAge: calculateAge(dobRaw),
        ageAtIssueDate: calculateAge(dobRaw, issueDateRaw),
        cardAge: calculateAge(issueDateRaw),
        photoBase64,
    };
}

connectToVps();