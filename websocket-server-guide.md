# WebSocket Server Configuration Guide

## Problem
The current WebSocket server only allows one connection per bridge ID, causing the hardware bridge to disconnect when web clients try to monitor the same bridge.

## Solution
Modify the server to support two types of clients:
1. **Bridge Clients** - Hardware bridges that send data (only one per bridge ID)
2. **Monitor Clients** - Web clients that receive data (multiple allowed)

## Server-Side Changes Needed

### 1. Authentication Handler
```javascript
// Handle different client types during authentication
io.use((socket, next) => {
    const { apiKey, clientType, bridgeId, targetBridgeId } = socket.handshake.auth;
    
    // Validate API key
    if (!isValidApiKey(apiKey)) {
        return next(new Error('Invalid API key'));
    }
    
    if (clientType === 'monitor') {
        // Monitor client - wants to watch a bridge
        socket.clientType = 'monitor';
        socket.targetBridgeId = targetBridgeId;
        socket.bridgeId = `monitor_${socket.id}_${targetBridgeId}`;
    } else {
        // Bridge client - hardware bridge
        socket.clientType = 'bridge';
        socket.bridgeId = bridgeId;
        
        // Check for duplicate bridge connections
        const existingBridge = findExistingBridge(bridgeId);
        if (existingBridge) {
            existingBridge.disconnect();
        }
    }
    
    next();
});
```

### 2. Connection Handler
```javascript
io.on('connection', (socket) => {
    if (socket.clientType === 'bridge') {
        console.log(`[${socket.bridgeId}] Bridge connected`);
        
        // Handle bridge events
        socket.on('bridgeEvent', (data) => {
            console.log(`[${socket.bridgeId}] Received event: ${data.status}`);
            
            // Broadcast to all monitors watching this bridge
            broadcastToMonitors(socket.bridgeId, 'bridgeEvent', data);
        });
        
    } else if (socket.clientType === 'monitor') {
        console.log(`[Monitor] Client monitoring bridge: ${socket.targetBridgeId}`);
        
        // Join a room for this bridge's monitors
        socket.join(`monitors_${socket.targetBridgeId}`);
        
        // Send current bridge status if available
        const bridgeStatus = getCurrentBridgeStatus(socket.targetBridgeId);
        if (bridgeStatus) {
            socket.emit('bridgeEvent', bridgeStatus);
        }
    }
});
```

### 3. Broadcasting Function
```javascript
function broadcastToMonitors(bridgeId, eventName, data) {
    // Send to all monitors watching this bridge
    io.to(`monitors_${bridgeId}`).emit(eventName, {
        source: bridgeId,
        timestamp: new Date().toISOString(),
        ...data
    });
}
```

## Client-Side Changes (Already Implemented)

### Web Client Authentication
```javascript
socket = io(serverUrl, {
    auth: {
        apiKey: 'your_api_key',
        clientType: 'monitor',           // Identify as monitor
        targetBridgeId: 'bridge_id'      // Bridge to monitor
    }
});
```

### Hardware Bridge Authentication (No Change)
```javascript
socket = io(serverUrl, {
    auth: {
        apiKey: 'your_api_key',
        bridgeId: 'bridge_id'            // Bridge identity
    }
});
```

## Expected Behavior

1. **Hardware Bridge Connects**: 
   - Identified as bridge client
   - Only one allowed per bridge ID
   - Can send events to server

2. **Web Client Connects**:
   - Identified as monitor client
   - Multiple allowed per bridge ID
   - Receives events from target bridge
   - Cannot send bridge events

3. **Event Flow**:
   ```
   Hardware Bridge → Server → All Monitor Clients
   ```

## Testing

1. Start hardware bridge
2. Open multiple `read.html` pages
3. All should connect successfully as monitors
4. Insert card in hardware bridge
5. All web clients should receive the same data

## Current Web Client Features

- ✅ Connects as monitor client
- ✅ Listens for multiple event types
- ✅ Enhanced debugging
- ✅ Flexible event handling
- ✅ Test function (Ctrl+T)

The web clients are now ready - the server needs to be updated to support the monitor client type.
