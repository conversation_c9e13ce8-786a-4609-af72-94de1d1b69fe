// read.js - WebSocket client for Thai ID Card Reader

// Configuration - Update these to match your server settings
const CONFIG = {
    // Environment settings
    environment: 'local', // 'local' or 'production'

    // Server URLs
    servers: {
        local: 'ws://localhost:3000',
        production: 'wss://ws.hompok.com'
    },

    // Get current server URL based on environment
    get serverUrl() {
        return this.servers[this.environment];
    },

    bridgeId: 'caremat::Registration_Point_1', // Should match the bridge ID
    apiKey: 'caremat_secret_key_for_reg_point_1_abcdef123456', // API key for authentication
    reconnectInterval: 5000,
    maxReconnectAttempts: 10
};

// Global variables
let socket = null;
let reconnectAttempts = 0;
let currentCardData = null;

// DOM elements
const environmentSelect = document.getElementById('environmentSelect');
const reconnectBtn = document.getElementById('reconnectBtn');
const connectionStatus = document.getElementById('connectionStatus');
const readerStatus = document.getElementById('readerStatus');
const cardStatus = document.getElementById('cardStatus');
const logContainer = document.getElementById('logContainer');
const cardPhoto = document.getElementById('cardPhoto');
const photoPlaceholder = document.getElementById('photoPlaceholder');

// Form fields
const formFields = {
    citizenId: document.getElementById('citizenId'),
    titleTH: document.getElementById('titleTH'),
    firstNameTH: document.getElementById('firstNameTH'),
    lastNameTH: document.getElementById('lastNameTH'),
    titleEN: document.getElementById('titleEN'),
    firstNameEN: document.getElementById('firstNameEN'),
    lastNameEN: document.getElementById('lastNameEN'),
    gender: document.getElementById('gender'),
    dob: document.getElementById('dob'),
    religion: document.getElementById('religion'),
    currentAge: document.getElementById('currentAge'),
    addressFull: document.getElementById('addressFull'),
    houseNo: document.getElementById('houseNo'),
    villageNo: document.getElementById('villageNo'),
    lane: document.getElementById('lane'),
    road: document.getElementById('road'),
    subDistrict: document.getElementById('subDistrict'),
    district: document.getElementById('district'),
    province: document.getElementById('province'),
    postcode: document.getElementById('postcode'),
    issuer: document.getElementById('issuer'),
    issueDate: document.getElementById('issueDate'),
    expireDate: document.getElementById('expireDate'),
    cardAge: document.getElementById('cardAge')
};

// Utility functions
function addLog(message) {
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = document.createElement('p');
    logEntry.innerHTML = `<span style="color: #9ca3af;">[${timestamp}]</span> ${message}`;
    logContainer.appendChild(logEntry);
    logContainer.scrollTop = logContainer.scrollHeight;
    
    // Limit log entries to prevent memory issues
    if (logContainer.children.length > 100) {
        logContainer.removeChild(logContainer.firstChild);
    }
}

function setStatus(element, text, color) {
    const dot = element.querySelector('.status-dot');
    const textSpan = element.querySelector('span:last-child');
    
    dot.className = `status-dot bg-${color}-500 mr-2`;
    if (color === 'yellow') {
        dot.classList.add('animate-pulse');
    }
    textSpan.textContent = text;
}

function clearCardData() {
    currentCardData = null;
    
    // Clear all form fields
    Object.values(formFields).forEach(field => {
        field.value = '';
    });
    
    // Hide photo and show placeholder
    cardPhoto.classList.add('hidden');
    cardPhoto.src = '';
    photoPlaceholder.classList.remove('hidden');
    
    addLog('[INFO] ล้างข้อมูลบัตรแล้ว');
}

function populateCardData(data) {
    currentCardData = data;
    
    // Populate basic fields
    formFields.citizenId.value = data.citizenId || '';
    formFields.titleTH.value = data.titleTH || '';
    formFields.firstNameTH.value = data.firstNameTH || '';
    formFields.lastNameTH.value = data.lastNameTH || '';
    formFields.titleEN.value = data.titleEN || '';
    formFields.firstNameEN.value = data.firstNameEN || '';
    formFields.lastNameEN.value = data.lastNameEN || '';
    formFields.gender.value = data.gender || '';
    formFields.dob.value = data.dob || '';
    formFields.religion.value = data.religion || '';
    formFields.currentAge.value = data.currentAge || '';
    formFields.addressFull.value = data.addressFull || '';
    formFields.issuer.value = data.issuer || '';
    formFields.issueDate.value = data.issueDate || '';
    formFields.expireDate.value = data.expireDate || '';
    formFields.cardAge.value = data.cardAge || '';
    
    // Populate address fields
    if (data.address) {
        formFields.houseNo.value = data.address.houseNo || '';
        formFields.villageNo.value = data.address.villageNo || '';
        formFields.lane.value = data.address.lane || '';
        formFields.road.value = data.address.road || '';
        formFields.subDistrict.value = data.address.subDistrict || '';
        formFields.district.value = data.address.district || '';
        formFields.province.value = data.address.province || '';
        formFields.postcode.value = data.address.postcode || '';
    }
    
    // Handle photo
    if (data.photoBase64) {
        cardPhoto.src = data.photoBase64;
        cardPhoto.classList.remove('hidden');
        photoPlaceholder.classList.add('hidden');
    } else {
        cardPhoto.classList.add('hidden');
        cardPhoto.src = '';
        photoPlaceholder.classList.remove('hidden');
    }
    
    addLog(`[SUCCESS] โหลดข้อมูลบัตรสำเร็จ: ${data.firstNameTH} ${data.lastNameTH}`);
}

// WebSocket connection functions
function connectToServer() {
    try {
        addLog(`[INFO] กำลังเชื่อมต่อกับ ${CONFIG.serverUrl}...`);
        setStatus(connectionStatus, 'กำลังเชื่อมต่อ...', 'yellow');
        
        socket = io(CONFIG.serverUrl, {
            transports: ['websocket', 'polling'],
            timeout: 10000,
            forceNew: true,
            auth: {
                apiKey: CONFIG.apiKey,
                bridgeId: CONFIG.bridgeId
            }
        });
        
        socket.on('connect', () => {
            addLog(`[SUCCESS] เชื่อมต่อสำเร็จ! Socket ID: ${socket.id}`);
            setStatus(connectionStatus, 'เชื่อมต่อแล้ว', 'green');
            reconnectAttempts = 0;
        });
        
        socket.on('disconnect', (reason) => {
            addLog(`[WARN] การเชื่อมต่อถูกตัด: ${reason}`);
            setStatus(connectionStatus, 'ตัดการเชื่อมต่อ', 'red');
            setStatus(readerStatus, 'ไม่ทราบสถานะ', 'gray');
            setStatus(cardStatus, 'ไม่ทราบสถานะ', 'gray');
            
            // Auto-reconnect
            if (reason !== 'io client disconnect') {
                setTimeout(connectToServer, CONFIG.reconnectInterval);
            }
        });
        
        socket.on('connect_error', (error) => {
            addLog(`[ERROR] เชื่อมต่อล้มเหลว: ${error.message}`);
            setStatus(connectionStatus, 'เชื่อมต่อล้มเหลว', 'red');
            
            reconnectAttempts++;
            if (reconnectAttempts < CONFIG.maxReconnectAttempts) {
                addLog(`[INFO] พยายามเชื่อมต่อใหม่ครั้งที่ ${reconnectAttempts}/${CONFIG.maxReconnectAttempts}...`);
                setTimeout(connectToServer, CONFIG.reconnectInterval);
            } else {
                addLog(`[ERROR] ไม่สามารถเชื่อมต่อได้หลังจากพยายาม ${CONFIG.maxReconnectAttempts} ครั้ง`);
            }
        });
        
        // Listen for bridge events
        socket.on('bridgeEvent', (data) => {
            handleBridgeEvent(data);
        });
        
    } catch (error) {
        addLog(`[ERROR] เกิดข้อผิดพลาดในการเชื่อมต่อ: ${error.message}`);
        setStatus(connectionStatus, 'เกิดข้อผิดพลาด', 'red');
    }
}

function handleBridgeEvent(data) {
    // Only process events from our target bridge
    if (data.source !== CONFIG.bridgeId) {
        return;
    }
    
    addLog(`[BRIDGE] ${data.status}: ${JSON.stringify(data.payload)}`);
    
    switch (data.status) {
        case 'reader_connected':
            setStatus(readerStatus, `เชื่อมต่อแล้ว (${data.payload.readerName})`, 'green');
            break;
            
        case 'reader_disconnected':
            setStatus(readerStatus, `อุปกรณ์ถูกถอด (${data.payload.readerName})`, 'red');
            setStatus(cardStatus, 'ไม่มีบัตร', 'gray');
            break;
            
        case 'reading':
            setStatus(cardStatus, 'กำลังอ่านข้อมูล...', 'yellow');
            break;
            
        case 'card_read_success':
            setStatus(cardStatus, 'อ่านข้อมูลสำเร็จ', 'green');
            if (data.payload && data.payload.data) {
                populateCardData(data.payload.data);
            }
            break;
            
        case 'card_read_error':
            setStatus(cardStatus, 'อ่านบัตรล้มเหลว', 'red');
            addLog(`[ERROR] ${data.payload.message}`);
            break;
            
        case 'card_removed':
            setStatus(cardStatus, 'นำบัตรออกแล้ว', 'gray');
            break;
    }
}

// Environment and connection handlers
environmentSelect.addEventListener('change', (e) => {
    CONFIG.environment = e.target.value;
    addLog(`[INFO] เปลี่ยนสภาพแวดล้อมเป็น: ${CONFIG.environment} (${CONFIG.serverUrl})`);
});

reconnectBtn.addEventListener('click', () => {
    if (socket) {
        socket.disconnect();
    }
    reconnectAttempts = 0;
    connectToServer();
});

// Button event handlers
document.getElementById('clearBtn').addEventListener('click', clearCardData);

document.getElementById('copyBtn').addEventListener('click', () => {
    if (currentCardData) {
        navigator.clipboard.writeText(JSON.stringify(currentCardData, null, 2))
            .then(() => addLog('[INFO] คัดลอก JSON ข้อมูลแล้ว'))
            .catch(err => addLog(`[ERROR] ไม่สามารถคัดลอกได้: ${err.message}`));
    } else {
        addLog('[WARN] ไม่มีข้อมูลบัตรให้คัดลอก');
    }
});

document.getElementById('saveBtn').addEventListener('click', () => {
    if (currentCardData) {
        // This is where you would implement saving to your database
        // For now, just show the data that would be saved
        addLog('[INFO] ข้อมูลที่จะบันทึก:');
        addLog(JSON.stringify(currentCardData, null, 2));
        
        // Example: Send to your API endpoint
        // fetch('/api/save-card-data', {
        //     method: 'POST',
        //     headers: { 'Content-Type': 'application/json' },
        //     body: JSON.stringify(currentCardData)
        // });
        
        alert('ฟีเจอร์บันทึกข้อมูลยังไม่ได้เชื่อมต่อกับฐานข้อมูล\nกรุณาดูข้อมูลใน Console Log');
    } else {
        addLog('[WARN] ไม่มีข้อมูลบัตรให้บันทึก');
    }
});

// Initialize connection when page loads
document.addEventListener('DOMContentLoaded', () => {
    addLog('[INFO] เริ่มต้นระบบ Thai ID Card Reader Web Interface');

    // Set initial environment
    environmentSelect.value = CONFIG.environment;

    connectToServer();
});

// Handle page unload
window.addEventListener('beforeunload', () => {
    if (socket) {
        socket.disconnect();
    }
});
